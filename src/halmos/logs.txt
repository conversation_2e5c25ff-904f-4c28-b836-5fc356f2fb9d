Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_e195bc4 = 0x8000000000000000
    halmos_block_timestamp_depth2_991c424 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3a5dee7_36 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3a6a4e6_03 = 
0x00
    p_decimals_uint8_67f0ee3_04 = 0x00
    p_manager_address_1c1a44c_37 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::add_new_asset(p_decimals_uint8_67f0ee3_04) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3a6a4e6_03) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f757c53_02)
        CREATE 0xaaaa0004::<7875 bytes of initcode>
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @0 ← 
0x5465737420546f6b656e00000000000000000000000000000000000000000014
            SLOAD  @1 → 
0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @1 ← 
0x5453540000000000000000000000000000000000000000000000000000000006
            SLOAD  @0 → 
0x5465737420546f6b656e00000000000000000000000000000000000000000014
        ↩ RETURN <5873 bytes of code>
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        SSTORE @4 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c → 0x00
        SSTORE 
@0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c ← 
0x00000000000000000000000000000000000000000000000000000000aaaa0004
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SSTORE 
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SSTORE @3 ← 
0x00000000000000000000000000000000000000000000000000000000aaaa0004
    ↩ RETURN 0x00000000000000000000000000000000000000000000000000000000aaaa0004
    CALL CryticToFoundry::setTheManager(p_manager_address_1c1a44c_37) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3a5dee7_36) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8ae83cb_35)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_1c1a44c_37))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_19151a9 = 0x8000000000000000
    halmos_block_timestamp_depth2_cb5070a = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6c30c77_41 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_89aae09_07 = 
0x00
    p_amt_uint128_af37e58_09 = 0x00
    p_manager_address_98778f5_42 = 0x8000000000000000000000000000000000000000
    p_to_address_aefd5ad_08 = 0x00
Sequence:
    CALL CryticToFoundry::asset_approve(Concat(p_to_address_aefd5ad_08, 
p_amt_uint128_af37e58_09)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_89aae09_07) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8c0257a_06)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @0 → 
0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::approve(Concat(0x000000000000000000000000, 
Extract(0x9f, 0x00, p_to_address_aefd5ad_08), 
0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_af37e58_09))) (caller: CryticToFoundry)
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_aefd5ad_08), 
0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_4_4_1024_7a5af03_09, 
Concat(Concat(Concat(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62
b3e1496, 0x0000000000000000000000000000000000000000000000000000000000000000), 
Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_aefd5ad_08))), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_aefd5ad_08), 
0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) ← 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_af37e58_09))
            LOG3(topic0=0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200a
c8c7c3b925, topic1=0x7fa9385be102ac3eac297483dd6233d62b3e1496, 
topic2=Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_aefd5ad_08)), data=Concat(0x00000000000000000000000000000000, 
Extract(0x7f, 0x00, p_amt_uint128_af37e58_09)))
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_98778f5_42) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6c30c77_41) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6aed162_40)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_98778f5_42))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_5c2cf58 = 0x8000000000000000
    halmos_block_timestamp_depth2_a9af3a4 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_a1d9f85_12 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_bea0a27_46 = 
0x00
    p_amt_uint128_0cf4d41_14 = 0x00
    p_manager_address_33d7219_47 = 0x8000000000000000000000000000000000000000
    p_to_address_12066a6_13 = 0x00
Sequence:
    CALL CryticToFoundry::asset_mint(Concat(p_to_address_12066a6_13, 
p_amt_uint128_0cf4d41_14)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_a1d9f85_12) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_de96d8a_11)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::mint(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_12066a6_13), 0x00000000000000000000000000000000, 
Extract(0x7f, 0x00, p_amt_uint128_0cf4d41_14))) (caller: CryticToFoundry)
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SSTORE @2 ← 
+(0x000000000000000000000000000000000000000001fffffffffffffffffffffe, 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_0cf4d41_14)))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_12066a6_13), 
0x0000000000000000000000000000000000000000000000000000000000000003)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_12066a6_13)), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_12066a6_13), 
0x0000000000000000000000000000000000000000000000000000000000000003)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_12066a6_13)), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_12066a6_13), 
0x0000000000000000000000000000000000000000000000000000000000000003)) ← 
+(Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_12066a6_13),
0x0000000000000000000000000000000000000000000000000000000000000000)), 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_0cf4d41_14)))
            LOG3(topic0=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a
4df523b3ef, topic1=0x00, topic2=Concat(0x000000000000000000000000, Extract(0x9f,
0x00, p_to_address_12066a6_13)), data=Concat(0x00000000000000000000000000000000,
Extract(0x7f, 0x00, p_amt_uint128_0cf4d41_14)))
        ↩ RETURN 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_33d7219_47) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_bea0a27_46) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3033e7c_45)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_33d7219_47))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_209d5c4 = 0x8000000000000000
    halmos_block_timestamp_depth2_97e5189 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_399f60b_17 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_5f3774d_49 = 
0x00
    p_manager_address_78b62f6_50 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::counter_increment_asAdmin() (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_399f60b_17) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_2804266_16)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        CALL 0xaaaa0003::increment() (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
            SSTORE @0 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        ↩ RETURN 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_78b62f6_50) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_5f3774d_49) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_599147c_48)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_78b62f6_50))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_4baf2ac = 0x8000000000000000
    halmos_block_timestamp_depth2_c61065c = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_08ad43a_56 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_0ab808d_23 = 
0x00
    p_manager_address_d24e0fa_57 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::increaseAmt(p_amount_uint256_dcb5661_24) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_0ab808d_23) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_ccccb1e_22)
        SLOAD  @11 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SLOAD  @11 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @11 ← p_amount_uint256_dcb5661_24
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_d24e0fa_57) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_08ad43a_56) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_657568d_55)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_d24e0fa_57))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_f22e58b = 0x8000000000000000
    halmos_block_timestamp_depth2_60b877a = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8a4e8aa_61 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_b97c969_27 = 
0x00
    p_isManager_bool_2543841_29 = 0x01
    p_manager_address_2ad8165_28 = 0x00
    p_manager_address_32ad7ae_62 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setIsManager(Concat(p_manager_address_2ad8165_28, 
p_isManager_bool_2543841_29)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_b97c969_27) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_c88a5a4_26)
        SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_manager_address_2ad8165_28), 
0x0000000000000000000000000000000000000000000000000000000000000009)) → 0x00
        SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_manager_address_2ad8165_28), 
0x0000000000000000000000000000000000000000000000000000000000000009)) ← 
Concat(0x00000000000000000000000000000000000000000000000000000000000000, 
If(==(p_isManager_bool_2543841_29, 
0x0000000000000000000000000000000000000000000000000000000000000000), 0x00, 
0x01))
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_32ad7ae_62) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8a4e8aa_61) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_cad94bf_60)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_32ad7ae_62))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_6673d2e = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8bc608a_36 = 
0x00
    p_decimals_uint8_61fa7f9_37 = 0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::add_new_asset(p_decimals_uint8_61fa7f9_37) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_8bc608a_36) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_ab71ee7_35)
        CREATE 0xaaaa0004::<7875 bytes of initcode>
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @0 ← 
0x5465737420546f6b656e00000000000000000000000000000000000000000014
            SLOAD  @1 → 
0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @1 ← 
0x5453540000000000000000000000000000000000000000000000000000000006
            SLOAD  @0 → 
0x5465737420546f6b656e00000000000000000000000000000000000000000014
        ↩ RETURN <5873 bytes of code>
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        SSTORE @4 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c → 0x00
        SSTORE 
@0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c ← 
0x00000000000000000000000000000000000000000000000000000000aaaa0004
        SLOAD  @4 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SSTORE 
@0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SSTORE @3 ← 
0x00000000000000000000000000000000000000000000000000000000aaaa0004
    ↩ RETURN 0x00000000000000000000000000000000000000000000000000000000aaaa0004

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_fcb7110 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_a1111dd_40 = 
0x00
    p_amt_uint128_e22e8a3_42 = 0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
    p_to_address_589f45b_41 = 0x00
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::asset_approve(Concat(p_to_address_589f45b_41, 
p_amt_uint128_e22e8a3_42)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_a1111dd_40) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_5bbe799_39)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @0 → 
0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::approve(Concat(0x000000000000000000000000, 
Extract(0x9f, 0x00, p_to_address_589f45b_41), 
0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_e22e8a3_42))) (caller: CryticToFoundry)
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_589f45b_41), 
0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_4_4_1024_7a5af03_09, 
Concat(Concat(Concat(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62
b3e1496, 0x0000000000000000000000000000000000000000000000000000000000000000), 
Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_589f45b_41))), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_589f45b_41), 
0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) ← 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_e22e8a3_42))
            LOG3(topic0=0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200a
c8c7c3b925, topic1=0x7fa9385be102ac3eac297483dd6233d62b3e1496, 
topic2=Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_589f45b_41)), data=Concat(0x00000000000000000000000000000000, 
Extract(0x7f, 0x00, p_amt_uint128_e22e8a3_42)))
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_76b1cd0 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_c585597_45 = 
0x00
    p_amt_uint128_1a67a8f_47 = 0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
    p_to_address_7d85b2f_46 = 0x00
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::asset_mint(Concat(p_to_address_7d85b2f_46, 
p_amt_uint128_1a67a8f_47)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_c585597_45) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_fe57d20_44)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::mint(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_7d85b2f_46), 0x00000000000000000000000000000000, 
Extract(0x7f, 0x00, p_amt_uint128_1a67a8f_47))) (caller: CryticToFoundry)
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 
0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SSTORE @2 ← 
+(0x000000000000000000000000000000000000000001fffffffffffffffffffffe, 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_1a67a8f_47)))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_7d85b2f_46), 
0x0000000000000000000000000000000000000000000000000000000000000003)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_7d85b2f_46)), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_7d85b2f_46), 
0x0000000000000000000000000000000000000000000000000000000000000003)) → 
Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_to_address_7d85b2f_46)), 
0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_to_address_7d85b2f_46), 
0x0000000000000000000000000000000000000000000000000000000000000003)) ← 
+(Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_11130eb_08, 
Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_7d85b2f_46),
0x0000000000000000000000000000000000000000000000000000000000000000)), 
Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, 
p_amt_uint128_1a67a8f_47)))
            LOG3(topic0=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a
4df523b3ef, topic1=0x00, topic2=Concat(0x000000000000000000000000, Extract(0x9f,
0x00, p_to_address_7d85b2f_46)), data=Concat(0x00000000000000000000000000000000,
Extract(0x7f, 0x00, p_amt_uint128_1a67a8f_47)))
        ↩ RETURN 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_18b1c36 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_014e669_50 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::counter_increment_asAdmin() (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_014e669_50) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_ca3ae61_49)
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 
0x0000000000000000000000000000000000000000000000000000000000000001
        CALL 
hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) 
(caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        CALL 0xaaaa0003::increment() (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000001
            SSTORE @0 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
        ↩ RETURN 0x
        SLOAD  @6 → 
0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        ↩ RETURN 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @13 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 
0x0000000000000000000000000000000000000000000000000000000000000002
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_475e762 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_812ec2a_56 = 
0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::increaseAmt(p_amount_uint256_5568c09_57) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_812ec2a_56) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3e5d1d4_55)
        SLOAD  @11 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SLOAD  @11 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @11 ← p_amount_uint256_5568c09_57
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_5569178 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_be9693c_60 = 
0x00
    p_isManager_bool_9086362_62 = 0x01
    p_manager_address_09af8d2_61 = 0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::setIsManager(Concat(p_manager_address_09af8d2_61, 
p_isManager_bool_9086362_62)) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_be9693c_60) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_280c82e_59)
        SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_manager_address_09af8d2_61), 
0x0000000000000000000000000000000000000000000000000000000000000009)) → 0x00
        SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 
0x00, p_manager_address_09af8d2_61), 
0x0000000000000000000000000000000000000000000000000000000000000009)) ← 
Concat(0x00000000000000000000000000000000000000000000000000000000000000, 
If(==(p_isManager_bool_9086362_62, 
0x0000000000000000000000000000000000000000000000000000000000000000), 0x00, 
0x01))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_8881694 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f1b0288_65 = 
0x00
    p_manager_address_83d2b33_33 = 0x00
    p_manager_address_cf08d84_66 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_cf08d84_66) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f1b0288_65) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_1097e00_64)
        SLOAD  @10 → Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_cf08d84_66))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_ce03e3b = 0x8000000000000000
    halmos_block_timestamp_depth2_4cc08e2 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3aaa48a_69 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32 = 
0x00
    p_entropy_uint256_3ba2165_70 = 0x00
    p_manager_address_83d2b33_33 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6079ae5_32) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d3e1193_31)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_83d2b33_33))
    ↩ RETURN 0x
    CALL CryticToFoundry::switchActor(p_entropy_uint256_3ba2165_70) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3aaa48a_69) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f38c32c_68)
        SLOAD  @1 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@+(0xb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf6, 
p_entropy_uint256_3ba2165_70) → 
Select(storage_0x7fa9385be102ac3eac297483dd6233d62b3e1496_1_1_256_14378b1_03, 
+(0x0000000000000000000000000000000000000000000000000000000000000000, 
p_entropy_uint256_3ba2165_70))
        SLOAD  @0 → 
0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        SSTORE @0 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
Select(storage_0x7fa9385be102ac3eac297483dd6233d62b3e1496_1_1_256_14378b1_03, 
p_entropy_uint256_3ba2165_70)))
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_6c7bfa9 = 0x8000000000000000
    halmos_block_timestamp_depth2_ad1396a = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_31225f5_36 = 
0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_e23a43b_69 = 
0x00
    p_entropy_uint256_bf67ff6_37 = 0x00
    p_manager_address_b8e5817_70 = 0x8000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::switchActor(p_entropy_uint256_bf67ff6_37) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_31225f5_36) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_9282034_35)
        SLOAD  @1 → 
0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  
@+(0xb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf6, 
p_entropy_uint256_bf67ff6_37) → 
Select(storage_0x7fa9385be102ac3eac297483dd6233d62b3e1496_1_1_256_14378b1_03, 
+(0x0000000000000000000000000000000000000000000000000000000000000000, 
p_entropy_uint256_bf67ff6_37))
        SLOAD  @0 → 
0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        SSTORE @0 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
Select(storage_0x7fa9385be102ac3eac297483dd6233d62b3e1496_1_1_256_14378b1_03, 
p_entropy_uint256_bf67ff6_37)))
    ↩ RETURN 0x
    CALL CryticToFoundry::setTheManager(p_manager_address_b8e5817_70) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_e23a43b_69) (caller:
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_22a8176_68)
        SLOAD  @10 → 
0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @10 ← Concat(0x000000000000000000000000, Extract(0x9f, 0x00, 
p_manager_address_b8e5817_70))
    ↩ RETURN 0x

[FAIL] invariant_never_manager() (paths: 90, time: 0.56s, bounds: [])
Symbolic test result: 0 passed; 4 failed; time: 4.96s
entreprenerd2@MacBook-Pro-di-Entreprenerd Halmos-Broken-Properties-Example % 