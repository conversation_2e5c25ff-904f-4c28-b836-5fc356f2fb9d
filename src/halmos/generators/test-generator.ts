import { TEMPLATE_PARTS } from "../constants";
import type { PropertyAndSequence } from "../../types/types";
import {
  type ParameterInfo,
  type FunctionParameter,
  parseParameterDeclaration,
  parseFunctionSignature,
  generateParameterDeclarations,
  matchParameterToType,
} from "../parsers/parameter-parser";
import { parseCallStatement } from "../parsers/call-parser";

export interface TestGenerationOptions {
  identifier: string;
  index: number;
}

export function generateTestFunction(
  propSeq: PropertyAndSequence,
  options: TestGenerationOptions
): string {
  const { identifier, index } = options;
  const functionName = createFunctionName(
    propSeq.brokenProperty,
    identifier,
    index
  );

  const sequences = Array.isArray(propSeq.sequence)
    ? propSeq.sequence
    : [propSeq.sequence];
  const { parameters, calls } = processSequences(sequences);

  const functionSignature = parseFunctionSignature(propSeq.brokenProperty);
  const paramDeclarations = generateParameterDeclarationsForFunction(
    functionSignature.parameters,
    parameters
  );
  const callStatements = generateCallStatements(calls, parameters);

  return assembleFunction(
    functionName,
    paramDeclarations,
    callStatements,
    propSeq.brokenProperty
  );
}

export function generateSequenceFunction(
  sequences: string[],
  brokenProperty: string,
  identifier: string
): string {
  const functionName = createSequenceFunctionName(brokenProperty, identifier);
  const { parameters, calls } = processSequences(sequences);

  const functionSignature = parseFunctionSignature(brokenProperty);
  const paramDeclarations = generateParameterDeclarationsForFunction(
    functionSignature.parameters,
    parameters
  );
  const callStatements = generateCallStatements(calls, parameters);

  return assembleFunction(
    functionName,
    paramDeclarations,
    callStatements,
    brokenProperty
  );
}

function createFunctionName(
  brokenProperty: string,
  identifier: string,
  index: number
): string {
  const cleanProperty = brokenProperty.replace(/[^a-zA-Z0-9_]/g, "_");
  return `${TEMPLATE_PARTS.FUNCTION_PREFIX}${identifier}_${cleanProperty}_${index}`;
}

function createSequenceFunctionName(
  brokenProperty: string,
  identifier: string
): string {
  const cleanProperty = brokenProperty.replace(/[^a-zA-Z0-9_]/g, "_");
  return `${TEMPLATE_PARTS.FUNCTION_PREFIX}${identifier}_${cleanProperty}`;
}

function processSequences(sequences: string[]): {
  parameters: ParameterInfo[];
  calls: string[];
} {
  const parameters: ParameterInfo[] = [];
  const calls: string[] = [];

  sequences.forEach((line) => {
    const paramInfo = parseParameterDeclaration(line);
    if (paramInfo) {
      parameters.push(paramInfo);
    } else if (line.startsWith("CALL ")) {
      calls.push(line);
    }
  });

  return { parameters, calls };
}

function generateParameterDeclarationsForFunction(
  functionParams: FunctionParameter[],
  availableParams: ParameterInfo[]
): string[] {
  if (functionParams.length === 0) {
    return availableParams.map(
      (param) => `${param.type} ${param.name} = ${param.value};`
    );
  }

  return functionParams.map((param) => {
    const matchedParam = matchParameterToType(param.name, availableParams);
    const paramInfo = availableParams.find((p) => p.name === matchedParam);
    const value =
      paramInfo?.value ||
      generateParameterDeclarations([param])[0].split(" = ")[1];
    return `${param.type} ${param.name} = ${value};`;
  });
}

function generateCallStatements(
  calls: string[],
  parameters: ParameterInfo[]
): string[] {
  return calls.map((callLine) => {
    const parsedCall = parseCallStatement(callLine);
    if (!parsedCall) return `// ${callLine}`;

    const mappedParams = parsedCall.parameters.map((param: string) =>
      matchParameterToType(param, parameters)
    );

    return `${parsedCall.functionName}(${mappedParams.join(", ")});`;
  });
}

function assembleFunction(
  functionName: string,
  paramDeclarations: string[],
  callStatements: string[],
  brokenProperty: string
): string {
  const parts = [
    `${functionName}${TEMPLATE_PARTS.FUNCTION_SUFFIX}`,
    "",
    `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.SETUP_COMMENT}`,
    ...paramDeclarations.map((decl) => `    ${decl}`),
    "",
    `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.REPRO_COMMENT}`,
    ...callStatements.map((stmt) => `    ${stmt}`),
    "",
    `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.INVARIANT_COMMENT}`,
    `    ${TEMPLATE_PARTS.COMMENT_PREFIX}${brokenProperty}`,
    "",
    TEMPLATE_PARTS.FUNCTION_END,
  ];

  return parts.join("\n");
}
